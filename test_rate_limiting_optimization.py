#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试速率限制优化效果的脚本
"""

import sys
import asyncio
import logging
import time
from datetime import datetime

sys.path.append('.')

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('rate_limit_test')

async def test_optimized_rate_limiting():
    """测试优化后的速率限制"""
    logger.info("🧪 测试优化后的速率限制处理...")

    try:
        from pipeline_core_v2 import OptimizedSimulationPipeline

        # 使用优化的配置创建流水线
        pipeline = OptimizedSimulationPipeline(
            factors_file="new_test_factors.txt",
            max_slots=None,  # 从配置文件读取
            batch_size=None,  # 从配置文件读取
            config_file='config_v2.ini'
        )

        logger.info(f"📊 配置信息:")
        logger.info(f"   最大槽位数: {pipeline.max_slots}")
        logger.info(f"   批次大小: {pipeline.batch_size}")
        logger.info(f"   SimulationService最大并发: {pipeline.simulation_service.max_concurrent_tasks}")
        logger.info(f"   任务检查间隔: {pipeline.simulation_service.task_check_interval}秒")
        logger.info(f"   请求延迟: {pipeline.rate_limiter.config.request_delay}秒")

        start_time = time.time()
        result = await pipeline.run_pipeline(limit=10)  # 测试10个Alpha
        end_time = time.time()

        if result and result.get('status') == 'completed':
            logger.info("✅ 优化测试成功")
            logger.info(f"   成功Alpha: {result['successful_alphas']}/{result['total_alphas']}")
            logger.info(f"   成功率: {result['success_rate']:.1f}%")
            logger.info(f"   总耗时: {end_time - start_time:.2f}秒")
            logger.info(f"   平均每Alpha耗时: {(end_time - start_time) / result['total_alphas']:.2f}秒")
            logger.info(f"   自适应延迟: {pipeline.rate_limiter.adaptive_delay:.2f}秒")
            logger.info(f"   连续速率限制次数: {pipeline.rate_limiter.consecutive_rate_limits}")
            return True
        else:
            logger.error("❌ 优化测试失败")
            return False

    except Exception as e:
        logger.error(f"❌ 测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """测试配置加载"""
    logger.info("🔧 测试配置加载...")

    try:
        from pipeline_config_and_data_structures_v2 import create_simulation_config

        config, auth_service, db_service, rate_limit_config = create_simulation_config('config_v2.ini')

        logger.info("✅ 配置加载成功")
        logger.info(f"   批次大小: {config.batch_size}")
        logger.info(f"   最大并发任务: {rate_limit_config.max_concurrent_tasks}")
        logger.info(f"   任务检查间隔: {rate_limit_config.task_check_interval}秒")
        logger.info(f"   请求延迟: {rate_limit_config.request_delay}秒")
        logger.info(f"   重试延迟: {rate_limit_config.retry_delay}秒")
        logger.info(f"   最大重试次数: {rate_limit_config.max_retries}")
        logger.info(f"   安全系数: {rate_limit_config.safety_margin}")

        return True

    except Exception as e:
        logger.error(f"❌ 配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def compare_configurations():
    """对比优化前后的配置"""
    logger.info("📊 配置对比:")

    logger.info("   优化前:")
    logger.info("     - 最大槽位数: 8")
    logger.info("     - 批次大小: 100")
    logger.info("     - SimulationService并发: 8")
    logger.info("     - 无智能速率控制")
    logger.info("     - 固定30秒等待")

    logger.info("   优化后:")
    logger.info("     - 最大槽位数: 3 (从配置读取)")
    logger.info("     - 批次大小: 500 (从配置读取)")
    logger.info("     - SimulationService并发: 3")
    logger.info("     - 智能自适应速率控制")
    logger.info("     - 指数退避重试机制")
    logger.info("     - 请求间隔控制")

async def main():
    """主测试函数"""
    logger.info("🚀 开始速率限制优化测试")
    logger.info("=" * 80)

    # 测试配置加载
    config_success = test_configuration()

    print("\n" + "=" * 80)

    # 配置对比
    compare_configurations()

    print("\n" + "=" * 80)

    if config_success:
        choice = input("配置测试通过！是否进行实际速率限制测试？(y/N): ").strip().lower()
        if choice == 'y':
            print("\n" + "=" * 80)
            simulation_success = await test_optimized_rate_limiting()

            if simulation_success:
                logger.info("🎉 所有测试通过！速率限制优化成功")
            else:
                logger.error("❌ 速率限制测试失败")
        else:
            logger.info("跳过实际测试")

    # 总结
    print("\n" + "=" * 80)
    logger.info("📋 优化总结:")
    logger.info("   1. 降低了并发数量 (8 -> 3)")
    logger.info("   2. 增加了批次大小 (100 -> 500)")
    logger.info("   3. 添加了智能速率控制")
    logger.info("   4. 实现了自适应延迟调整")
    logger.info("   5. 增强了错误处理和重试机制")
    logger.info("   6. 预期效果: 显著减少429错误，提高整体效率")

if __name__ == "__main__":
    asyncio.run(main())