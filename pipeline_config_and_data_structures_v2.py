#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化版本的流水线配置和数据结构
集成jy_worldquant项目的SimulationService
"""

# 导入类型提示相关的模块，用于代码注解，增强可读性和健壮性
from typing import Dict, List, Optional, Any, Tuple
# 导入datetime类，用于处理日期和时间
from datetime import datetime
# 从dataclasses模块导入dataclass装饰器，它能自动为类生成特殊方法，如__init__()
from dataclasses import dataclass
# 导入pandas库，用于数据处理和CSV文件操作
import pandas as pd
# 导入logging库
import logging
# 导入configparser用于读取配置文件
import configparser

# 导入jy_worldquant项目的相关模块
from jy_worldquant.common_config import CommonConfig
from jy_worldquant.common_auth import BrainAuth
from jy_worldquant.db_utils import setup_database
from jy_worldquant.core.simulation_service import SimulationService

# 默认的因子文件名，可在此处修改
DEFAULT_FACTORS_FILE = "filtered_shuffled_expressions.txt"

@dataclass # @dataclass装饰器：自动生成__init__、__repr__等样板代码
class SimulationTask: # 定义一个类，用于封装一个仿真任务的所有相关信息
    """仿真任务数据结构 - 使用SimulationService"""
    batch_id: int # 任务批次的唯一标识符
    alpha_list: List[Tuple[str, int]] # Alpha列表，每个元素为(expression, decay)元组
    factor_ids: List[str] # 存储该批次任务中所有alpha的因子ID
    expressions: List[str] # 存储该批次任务中所有alpha的表达式字符串
    submit_time: Optional[datetime] = None # 任务提交到API的时间，初始化时为None
    complete_time: Optional[datetime] = None # 任务完成的时间，初始化时为None
    status: str = "PENDING"  # 任务的当前状态，默认为"PENDING"
    successful_alphas: List[Tuple[str, int]] = None # 成功完成仿真的Alpha列表
    error_message: Optional[str] = None # 错误消息，如果有的话

@dataclass # @dataclass装饰器：同样用于简化类的定义
class PerformanceStats: # 定义一个类，用于跟踪和统计流水线运行期间的性能指标
    """实时性能统计""" # 类的文档字符串
    total_tasks: int = 0 # 流水线需要处理的总任务数，默认为0
    pending_tasks: int = 0 # 尚未开始处理的待定任务数，默认为0
    submitted_tasks: int = 0 # 已经提交但尚未完成的任务数，默认为0
    running_tasks: int = 0 # 当前正在运行的任务数，默认为0
    completed_tasks: int = 0 # 已经成功完成的任务数，默认为0
    failed_tasks: int = 0 # 执行失败的任务数，默认为0
    total_alphas: int = 0 # 需要处理的alpha因子总数，默认为0
    successful_alphas: int = 0 # 成功完成仿真的Alpha数量
    start_time: Optional[datetime] = None # 流水线开始运行的时间，初始化时为None
    active_slots: int = 0 # 当前正在工作的并发槽位数，默认为0
    total_slots: int = 8 # 配置的总并发槽位数，默认为8

    @property # 将一个方法转换为可以直接访问的属性
    def completion_rate(self) -> float: # 计算并返回任务的完成率
        # 如果总任务数大于0，则计算完成百分比；否则返回0，避免除零错误
        return (self.completed_tasks / self.total_tasks * 100) if self.total_tasks > 0 else 0

    @property # 将方法转换为属性
    def success_rate(self) -> float: # 计算并返回已处理任务的成功率
        # 如果已完成和失败的任务总数大于0，则计算成功百分比；否则返回0
        return (self.completed_tasks / (self.completed_tasks + self.failed_tasks) * 100) if (self.completed_tasks + self.failed_tasks) > 0 else 0

    @property # 将方法转换为属性
    def elapsed_time(self) -> float: # 计算并返回从开始到现在的总耗时（秒）
        # 如果start_time已设置，则计算当前时间与开始时间的差值
        return (datetime.now() - self.start_time).total_seconds() if self.start_time else 0

    @property # 将方法转换为属性
    def alphas_per_minute(self) -> float: # 计算并返回平均每分钟处理的alpha数量
        minutes = self.elapsed_time / 60 # 将总耗时从秒转换为分钟
        # 如果运行时间大于0，则计算每分钟的alpha处理速率；否则返回0
        return (self.successful_alphas / minutes) if minutes > 0 else 0

@dataclass
class RateLimitConfig:
    """速率限制配置"""
    max_concurrent_tasks: int = 3
    task_check_interval: int = 10
    request_delay: float = 2.0
    retry_delay: int = 60
    max_retries: int = 3
    safety_margin: float = 0.8

def create_simulation_config(config_file: str = "config_v2.ini") -> Tuple[CommonConfig, BrainAuth, Any, RateLimitConfig]:
    """
    创建仿真配置，初始化所需的服务

    Args:
        config_file: 配置文件路径

    Returns:
        (CommonConfig对象, BrainAuth对象, DatabaseService对象, RateLimitConfig对象)
    """
    # 读取配置文件
    config_parser = configparser.ConfigParser()
    config_parser.read(config_file, encoding='utf-8')

    # 创建通用配置对象
    common_config = CommonConfig.from_config(config_parser)

    # 创建认证服务
    auth_service = BrainAuth()

    # 创建数据库服务
    db_service = setup_database(config_parser)

    # 创建速率限制配置
    rate_limit_config = RateLimitConfig(
        max_concurrent_tasks=config_parser.getint('rate_limiting', 'max_concurrent_tasks', fallback=3),
        task_check_interval=config_parser.getint('rate_limiting', 'task_check_interval', fallback=10),
        request_delay=config_parser.getfloat('rate_limiting', 'request_delay', fallback=2.0),
        retry_delay=config_parser.getint('rate_limiting', 'retry_delay', fallback=60),
        max_retries=config_parser.getint('rate_limiting', 'max_retries', fallback=3),
        safety_margin=config_parser.getfloat('rate_limiting', 'safety_margin', fallback=0.8)
    )

    return common_config, auth_service, db_service, rate_limit_config

def load_alpha_factors(factors_file: str, logger: logging.Logger, limit: Optional[int] = None) -> List[Tuple[str, str]]:
    """加载Alpha因子数据，支持单列表达式txt格式"""
    logger.info(f"📁 开始加载因子文件: {factors_file}")
    try:
        if factors_file.endswith('.txt'):
            # 兼容单列表达式txt格式
            with open(factors_file, 'r', encoding='utf-8') as f:
                lines = [line.strip() for line in f if line.strip()]
            if limit:
                lines = lines[:limit]
            alpha_list = [(str(i+1), expr) for i, expr in enumerate(lines)]
            logger.info(f"✅ 成功加载 {len(alpha_list)} 个Alpha因子 (txt单列模式)")
            if alpha_list:
                logger.info("📋 前5个因子示例:")
                for i, (factor_id, expr) in enumerate(alpha_list[:5]):
                    display_expr = expr[:50] + "..." if len(expr) > 50 else expr
                    logger.info(f"  {i+1}. {factor_id}: {display_expr}")
            return alpha_list
        else:
            # CSV格式处理
            df = pd.read_csv(factors_file)
            logger.info(f"📊 原始数据形状: {df.shape}")
            # 检查列名并适配不同的文件格式
            if 'factor_id' in df.columns and 'expression' in df.columns:
                factor_id_col = 'factor_id'
                expression_col = 'expression'
            elif 'alpha_expression' in df.columns:
                factor_id_col = 'order'
                expression_col = 'alpha_expression'
                if 'order' not in df.columns:
                    df['order'] = range(1, len(df) + 1)
            else:
                raise ValueError(f"文件格式不支持，需要包含 'factor_id'+'expression' 或 'alpha_expression' 列")
            valid_data = df[[factor_id_col, expression_col]].dropna()
            if limit:
                valid_data = valid_data.head(limit)
            alpha_list = []
            for _, row in valid_data.iterrows():
                factor_id = str(row[factor_id_col])
                expression = str(row[expression_col])
                alpha_list.append((factor_id, expression))
            logger.info(f"✅ 成功加载 {len(alpha_list)} 个Alpha因子")
            if alpha_list:
                logger.info("📋 前5个因子示例:")
                for i, (factor_id, expr) in enumerate(alpha_list[:5]):
                    display_expr = expr[:50] + "..." if len(expr) > 50 else expr
                    logger.info(f"  {i+1}. {factor_id}: {display_expr}")
            return alpha_list
    except Exception as e:
        logger.error(f"❌ 加载因子文件失败: {str(e)}")
        return []