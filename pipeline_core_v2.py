#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
优化版本的流水线核心逻辑
使用jy_worldquant项目的SimulationService替换自定义回测实现
"""

# 导入asyncio库，用于支持异步IO操作和并发
import asyncio
# 导入time库，用于处理时间相关的操作，如获取时间戳、延时
import time
# 导入logging库，用于记录程序运行日志
import logging
# 导入pandas库，用于数据处理和CSV文件操作
import pandas as pd
# 从datetime库导入datetime和timedelta，用于处理日期和时间
from datetime import datetime, timedelta
# 从typing库导入类型提示，增强代码可读性
from typing import Dict, List, Optional, Tuple, Any
# 从collections库导入defaultdict，用于创建带有默认值的字典
from collections import defaultdict
# 导入json库，用于处理JSON格式的数据
import json
# 导入os库，用于与操作系统交互，如文件路径操作
import os
# 导入random库，用于生成随机数
import random
# 导入csv库，用于处理CSV文件操作
import csv

# 从配置文件中导入数据结构类和配置
from pipeline_config_and_data_structures_v2 import (
    SimulationTask, PerformanceStats, create_simulation_config,
    load_alpha_factors, DEFAULT_FACTORS_FILE, RateLimitConfig
)

# 导入jy_worldquant项目的相关模块
from jy_worldquant.core.simulation_service import SimulationService
from jy_worldquant.core.base_service import BaseService

class EnhancedSimulationService(SimulationService):
    """增强的仿真服务，具有更好的速率限制处理"""

    def __init__(self, config, auth_service, db_service=None, rate_limit_config=None):
        super().__init__(config, auth_service, db_service)
        self.rate_limit_config = rate_limit_config
        if rate_limit_config:
            self.max_concurrent_tasks = rate_limit_config.max_concurrent_tasks
            self.task_check_interval = rate_limit_config.task_check_interval

    def make_request(self, method: str, url: str, **kwargs):
        """重写请求方法，增强速率限制处理"""
        max_retries = self.rate_limit_config.max_retries if self.rate_limit_config else 3
        retry_delay = self.rate_limit_config.retry_delay if self.rate_limit_config else 60

        for attempt in range(max_retries):
            try:
                response = super().make_request(method, url, **kwargs)
                if response:
                    return response
            except Exception as e:
                if "429" in str(e) or "rate limit" in str(e).lower():
                    if attempt < max_retries - 1:
                        wait_time = retry_delay * (2 ** attempt)  # 指数退避
                        self.logger.warning(f"速率限制重试 {attempt + 1}/{max_retries}，等待 {wait_time}秒")
                        time.sleep(wait_time)
                        continue
                raise e

        return None

class SmartRateLimiter:
    """智能速率限制器"""

    def __init__(self, config: RateLimitConfig, logger: logging.Logger):
        self.config = config
        self.logger = logger
        self.request_history = []
        self.last_request_time = 0
        self.consecutive_rate_limits = 0
        self.adaptive_delay = config.request_delay

    async def wait_if_needed(self):
        """根据历史情况智能等待"""
        current_time = time.time()

        # 清理过期的请求历史
        cutoff_time = current_time - 60  # 保留最近1分钟的历史
        self.request_history = [t for t in self.request_history if t > cutoff_time]

        # 计算当前请求频率
        if len(self.request_history) >= 5:  # 如果最近1分钟内有5个或更多请求
            # 增加自适应延迟
            self.adaptive_delay = min(self.adaptive_delay * 1.2, 10.0)
            self.logger.debug(f"增加自适应延迟到 {self.adaptive_delay:.1f}秒")
        elif len(self.request_history) <= 2:
            # 减少自适应延迟
            self.adaptive_delay = max(self.adaptive_delay * 0.9, self.config.request_delay)

        # 确保请求间隔
        time_since_last = current_time - self.last_request_time
        if time_since_last < self.adaptive_delay:
            wait_time = self.adaptive_delay - time_since_last
            self.logger.debug(f"速率控制等待 {wait_time:.1f}秒")
            await asyncio.sleep(wait_time)

        # 记录请求时间
        self.last_request_time = time.time()
        self.request_history.append(self.last_request_time)

    def on_rate_limit_hit(self):
        """当触发速率限制时调用"""
        self.consecutive_rate_limits += 1
        # 指数退避
        self.adaptive_delay = min(self.adaptive_delay * 2, 30.0)
        self.logger.warning(f"触发速率限制 (连续第{self.consecutive_rate_limits}次)，调整延迟到 {self.adaptive_delay:.1f}秒")

    def on_request_success(self):
        """当请求成功时调用"""
        if self.consecutive_rate_limits > 0:
            self.consecutive_rate_limits = 0
            self.adaptive_delay = max(self.adaptive_delay * 0.8, self.config.request_delay)
            self.logger.info(f"请求恢复正常，调整延迟到 {self.adaptive_delay:.1f}秒")

class OptimizedSimulationPipeline:
    """优化的仿真流水线系统 - 使用SimulationService"""

    def __init__(self, factors_file: str = DEFAULT_FACTORS_FILE,
                 resume_from: int = 0,
                 random_seed: int = 42,
                 max_slots: int = None,  # 改为None，从配置文件读取
                 batch_size: int = None,  # 改为None，从配置文件读取
                 config_file: str = "config_v2.ini"):
        """
        初始化优化的仿真流水线

        Args:
            factors_file: 因子文件路径
            resume_from: 断点续传起始位置
            random_seed: 随机种子
            max_slots: 最大并发槽位数（None则从配置文件读取）
            batch_size: 批次大小（None则从配置文件读取）
            config_file: 配置文件路径
        """
        self.factors_file = factors_file
        self.resume_from = resume_from
        self.random_seed = random_seed
        self.config_file = config_file

        # 设置随机种子以保证结果的可复现性
        random.seed(random_seed)

        # 生成一个基于当前时间的时间戳，用于命名文件
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 初始化流水线的各个组件
        self.setup_logging()
        self.setup_services()

        # 从配置中获取参数（如果未指定）
        self.max_slots = max_slots if max_slots is not None else self.rate_limit_config.max_concurrent_tasks
        self.batch_size = batch_size if batch_size is not None else self.config.batch_size

        # 创建智能速率限制器
        self.rate_limiter = SmartRateLimiter(self.rate_limit_config, self.logger)

        # 初始化任务队列和状态管理列表
        self.task_queue = asyncio.Queue()
        self.completed_tasks: List[SimulationTask] = []
        self.failed_tasks: List[SimulationTask] = []
        self.active_tasks: Dict[int, SimulationTask] = {}

        # 进度记录系统
        self.progress_file = f"simulation_progress_{self.timestamp}.json"
        self.completed_task_count = 0

        # 初始化性能统计对象
        self.stats = PerformanceStats(total_slots=self.max_slots)

        # 日志显示控制
        self.last_stats_display = 0
        self.stats_display_interval = 30

        # 成功Alpha记录文件
        self.successful_alphas_file = f"successful_alphas_{self.timestamp}.csv"
        self._initialize_successful_alphas_file()

        self.logger.info(f"🚀 优化仿真流水线系统初始化完成")
        self.logger.info(f"📁 目标文件: {self.factors_file}")
        self.logger.info(f"🎲 随机种子: {self.random_seed}")
        self.logger.info(f"⚙️ 配置: {self.max_slots}槽位, 批次大小{self.batch_size}")
        if self.resume_from > 0:
            self.logger.info(f"🔄 断点续传: 从第{self.resume_from}个任务开始")

    def setup_logging(self):
        """设置优化的日志系统"""
        log_filename = f"simulation_pipeline_{self.timestamp}.log"

        # 创建一个自定义的日志格式化器，用于在控制台输出带颜色的日志
        class ColoredFormatter(logging.Formatter):
            """彩色日志格式化器"""

            COLORS = {
                'INFO': '\033[32m',      # 绿色
                'WARNING': '\033[33m',   # 黄色
                'ERROR': '\033[31m',     # 红色
                'RESET': '\033[0m'       # 重置颜色
            }

            def format(self, record):
                if hasattr(record, 'no_color'):
                    return super().format(record)

                color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
                reset = self.COLORS['RESET']
                record.msg = f"{color}{record.msg}{reset}"
                return super().format(record)

        # 配置基础的日志设置
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_filename, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )

        # 获取控制台处理器并进行单独配置
        console_handler = [h for h in logging.getLogger().handlers if isinstance(h, logging.StreamHandler)][0]
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(ColoredFormatter('%(levelname)s - %(message)s'))

        # 将其他库的日志级别设为WARNING，以减少不必要的干扰
        logging.getLogger('urllib3').setLevel(logging.WARNING)
        logging.getLogger('requests').setLevel(logging.WARNING)

        self.logger = logging.getLogger(self.__class__.__name__)

    def setup_services(self):
        """设置所需的服务"""
        try:
            # 创建配置和服务
            self.config, self.auth_service, self.db_service, self.rate_limit_config = create_simulation_config(self.config_file)

            # 创建增强的SimulationService实例
            self.simulation_service = EnhancedSimulationService(
                self.config,
                self.auth_service,
                self.db_service,
                self.rate_limit_config
            )

            self.logger.info("✅ 服务初始化成功")
            self.logger.info(f"📧 认证服务: {self.auth_service.__class__.__name__}")
            self.logger.info(f"💾 数据库服务: {self.db_service.__class__.__name__ if self.db_service else 'None'}")
            self.logger.info(f"🔬 仿真服务: {self.simulation_service.__class__.__name__}")
            self.logger.info(f"⚡ 速率限制: 最大并发{self.rate_limit_config.max_concurrent_tasks}, 检查间隔{self.rate_limit_config.task_check_interval}秒")

        except Exception as e:
            self.logger.error(f"❌ 服务初始化失败: {e}")
            raise

    def _initialize_successful_alphas_file(self):
        """初始化成功Alpha记录文件"""
        try:
            with open(self.successful_alphas_file, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['timestamp', 'batch_id', 'factor_id', 'expression', 'decay'])
            self.logger.info(f"📝 成功Alpha记录文件已初始化: {self.successful_alphas_file}")
        except Exception as e:
            self.logger.error(f"❌ 初始化成功Alpha记录文件失败: {e}")

    def _append_successful_alphas(self, batch_id: int, successful_alphas: List[Tuple[str, int]], factor_ids: List[str]):
        """追加成功的Alpha到记录文件"""
        try:
            with open(self.successful_alphas_file, 'a', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                timestamp = datetime.now().isoformat()
                for i, (expression, decay) in enumerate(successful_alphas):
                    factor_id = factor_ids[i] if i < len(factor_ids) else f"unknown_{i}"
                    writer.writerow([timestamp, batch_id, factor_id, expression, decay])
        except Exception as e:
            self.logger.error(f"❌ 追加成功Alpha记录失败: {e}")

    def save_progress(self):
        """保存当前进度"""
        actual_total_progress = self.resume_from + self.completed_task_count

        progress_data = {
            'timestamp': datetime.now().isoformat(),
            'completed_tasks': actual_total_progress,
            'total_tasks': self.stats.total_tasks,
            'total_alphas': self.stats.total_alphas,
            'successful_alphas': self.stats.successful_alphas,
            'failed_tasks': self.stats.failed_tasks,
            'random_seed': self.random_seed,
            'factors_file': self.factors_file,
            'max_progress_ever': actual_total_progress,
            'session_info': {
                'resume_from': self.resume_from,
                'session_completed': self.completed_task_count,
                'total_progress': actual_total_progress
            }
        }

        try:
            with open(self.progress_file, 'w', encoding='utf-8') as f:
                json.dump(progress_data, f, indent=2, ensure_ascii=False)
            self.logger.debug(f"💾 进度已保存: 总进度{actual_total_progress}个任务")
        except Exception as e:
            self.logger.error(f"❌ 保存进度文件失败: {e}")

    def create_simulation_tasks(self, alpha_list: List[Tuple[str, str]]) -> List[SimulationTask]:
        """将Alpha因子列表转换为仿真任务，支持断点续传"""
        self.logger.info(f"📦 创建仿真任务，每批{self.batch_size}个Alpha")

        # 支持断点续传：跳过已完成的任务
        if self.resume_from > 0:
            skip_count = self.resume_from * self.batch_size
            if skip_count < len(alpha_list):
                alpha_list = alpha_list[skip_count:]
                self.logger.info(f"🔄 断点续传: 跳过前{skip_count}个Alpha，剩余{len(alpha_list)}个")
            else:
                self.logger.warning(f"⚠️ 断点续传位置({skip_count})超出总Alpha数({len(alpha_list)})")
                return []

        tasks = []
        batch_id = self.resume_from + 1  # 从断点位置开始编号

        for i in range(0, len(alpha_list), self.batch_size):
            batch_alphas = alpha_list[i:i + self.batch_size]
            if len(batch_alphas) < 3:  # 跳过少于3个Alpha的批次
                if tasks:  # 如果已有任务，将剩余Alpha合并到最后一个任务
                    last_task = tasks[-1]
                    for factor_id, expression in batch_alphas:
                        last_task.factor_ids.append(factor_id)
                        last_task.expressions.append(expression)
                        last_task.alpha_list.append((expression, self.config.decay))
                    continue
                else:
                    self.logger.warning(f"⚠️ 跳过少于3个Alpha的批次: {len(batch_alphas)}")
                    continue

            factor_ids = [factor_id for factor_id, _ in batch_alphas]
            expressions = [expression for _, expression in batch_alphas]
            # 创建(expression, decay)元组列表，用于SimulationService
            alpha_tuples = [(expression, self.config.decay) for _, expression in batch_alphas]

            task = SimulationTask(
                batch_id=batch_id,
                alpha_list=alpha_tuples,
                factor_ids=factor_ids,
                expressions=expressions,
                successful_alphas=[]
            )
            tasks.append(task)
            batch_id += 1

        self.logger.info(f"✅ 创建{len(tasks)}个任务 (编号{self.resume_from + 1}-{batch_id - 1})")
        return tasks

    async def slot_worker(self, slot_id: int):
        """槽位工作协程，处理一个槽位的所有任务"""
        self.logger.info(f"🔧 槽位{slot_id} 启动")

        # 槽位级别的初始随机延迟
        initial_delay = random.uniform(0, 5)
        await asyncio.sleep(initial_delay)

        while True:
            try:
                task = await self.task_queue.get()
                if task is None:  # 结束信号
                    break

                # 标记槽位繁忙并更新统计数据
                self.active_tasks[slot_id] = task
                self.stats.active_slots += 1
                self.stats.submitted_tasks += 1
                self.stats.pending_tasks -= 1

                # 执行仿真任务
                success = await self.process_simulation_task(slot_id, task)

                if success:
                    self.completed_tasks.append(task)
                    self.stats.completed_tasks += 1
                    self.stats.running_tasks -= 1
                    self.completed_task_count += 1

                    if task.successful_alphas:
                        self.stats.successful_alphas += len(task.successful_alphas)
                        # 记录成功的Alpha
                        self._append_successful_alphas(task.batch_id, task.successful_alphas, task.factor_ids)

                    # 实时保存进度
                    self.save_progress()
                    if self.completed_task_count % 10 == 0:
                        total_progress = self.resume_from + self.completed_task_count
                        self.logger.info(f"💾 断点进度已保存: 总进度{total_progress}个任务")
                else:
                    self.failed_tasks.append(task)
                    self.stats.failed_tasks += 1
                    self.stats.running_tasks -= 1

                # 清理槽位状态并标记任务完成
                del self.active_tasks[slot_id]
                self.stats.active_slots -= 1
                self.task_queue.task_done()

                # 根据需要显示进度
                await self.display_progress_if_needed()

            except asyncio.CancelledError:
                self.logger.info(f"🛑 槽位{slot_id} 被取消")
                break
            except Exception as e:
                self.logger.error(f"❌ [槽位{slot_id}] 处理异常: {e}")
                # 清理状态
                if slot_id in self.active_tasks:
                    failed_task = self.active_tasks[slot_id]
                    failed_task.status = "FAILED"
                    failed_task.error_message = str(e)
                    self.failed_tasks.append(failed_task)
                    self.stats.failed_tasks += 1
                    self.stats.running_tasks -= 1
                    del self.active_tasks[slot_id]
                    self.stats.active_slots -= 1
                self.task_queue.task_done()
                await asyncio.sleep(5)  # 短暂休息后继续执行

        self.logger.info(f"🏁 槽位{slot_id} 结束")

    async def process_simulation_task(self, slot_id: int, task: SimulationTask) -> bool:
        """处理仿真任务"""
        try:
            self.logger.info(f"🔬 [槽位{slot_id}] 开始处理任务{task.batch_id}，包含{len(task.alpha_list)}个Alpha")

            # 应用智能速率限制
            await self.rate_limiter.wait_if_needed()

            task.submit_time = datetime.now()
            task.status = "RUNNING"
            self.stats.running_tasks += 1

            # 使用SimulationService进行仿真
            # 注意：这里需要在异步环境中调用同步的SimulationService
            loop = asyncio.get_event_loop()

            try:
                successful_alphas = await loop.run_in_executor(
                    None,
                    self.simulation_service.simulate_alphas,
                    task.alpha_list
                )

                # 请求成功，通知速率限制器
                self.rate_limiter.on_request_success()

            except Exception as sim_error:
                # 检查是否是速率限制错误
                if "429" in str(sim_error) or "rate limit" in str(sim_error).lower():
                    self.rate_limiter.on_rate_limit_hit()
                    # 等待更长时间后重试
                    await asyncio.sleep(self.rate_limit_config.retry_delay)
                    # 重试一次
                    successful_alphas = await loop.run_in_executor(
                        None,
                        self.simulation_service.simulate_alphas,
                        task.alpha_list
                    )
                else:
                    raise sim_error

            task.successful_alphas = successful_alphas
            task.complete_time = datetime.now()
            task.status = "COMPLETED"

            success_count = len(successful_alphas)
            total_count = len(task.alpha_list)

            self.logger.info(f"✅ [槽位{slot_id}] 任务{task.batch_id}完成: {success_count}/{total_count} Alpha成功")

            return True

        except Exception as e:
            self.logger.error(f"❌ [槽位{slot_id}] 任务{task.batch_id} 仿真异常: {e}")
            task.status = "FAILED"
            task.error_message = str(e)
            return False

    async def display_progress_if_needed(self):
        """根据需要显示进度"""
        current_time = time.time()
        if current_time - self.last_stats_display >= self.stats_display_interval:
            self.display_realtime_stats()
            self.last_stats_display = current_time

    def display_realtime_stats(self):
        """显示实时统计信息"""
        self.logger.info("=" * 60)
        self.logger.info("📊 实时统计")
        self.logger.info("=" * 60)
        self.logger.info(f"📋 总任务: {self.stats.total_tasks}")
        self.logger.info(f"⏳ 待处理: {self.stats.pending_tasks}")
        self.logger.info(f"🔄 运行中: {self.stats.running_tasks}")
        self.logger.info(f"✅ 已完成: {self.stats.completed_tasks}")
        self.logger.info(f"❌ 失败: {self.stats.failed_tasks}")
        self.logger.info(f"🎯 总Alpha: {self.stats.total_alphas}")
        self.logger.info(f"🆔 成功Alpha: {self.stats.successful_alphas}")
        self.logger.info(f"📈 完成率: {self.stats.completion_rate:.1f}%")
        self.logger.info(f"📈 成功率: {self.stats.success_rate:.1f}%")
        self.logger.info(f"⚡ 效率: {self.stats.alphas_per_minute:.1f} Alpha/分钟")
        self.logger.info(f"🔧 活跃槽位: {self.stats.active_slots}/{self.stats.total_slots}")
        self.logger.info("=" * 60)

    async def run_pipeline(self, limit: Optional[int] = None) -> Dict:
        """运行流水线系统"""
        start_time = time.time()
        self.stats.start_time = datetime.now()
        self.logger.info("🚀 启动优化仿真流水线系统")
        self.logger.info("=" * 80)

        try:
            # 加载Alpha因子
            self.logger.info("📁 加载Alpha因子...")
            alpha_list = load_alpha_factors(self.factors_file, self.logger, limit=limit)

            # 去重步骤
            unique_alpha_list = []
            seen_expr = set()
            for fid, expr in alpha_list:
                if expr not in seen_expr:
                    unique_alpha_list.append((fid, expr))
                    seen_expr.add(expr)
            if len(unique_alpha_list) < len(alpha_list):
                self.logger.info(f"💡 去除 {len(alpha_list)-len(unique_alpha_list)} 个重复Alpha表达式")
            alpha_list = unique_alpha_list

            if not alpha_list:
                return {'status': 'failed', 'error': '无法加载Alpha因子'}

            # 数据库过滤（如果启用）
            if self.db_service:
                self.logger.info("🔍 过滤已回测的Alpha...")
                # 转换为SimulationService需要的格式
                alpha_tuples = [(expr, self.config.decay) for _, expr in alpha_list]
                filtered_alphas = self.db_service.filter_new_alpha_list(alpha_tuples, self.config)
                # 转换回原格式
                filtered_alpha_list = []
                for expr, decay in filtered_alphas:
                    # 找到对应的factor_id
                    for fid, orig_expr in alpha_list:
                        if orig_expr == expr:
                            filtered_alpha_list.append((fid, expr))
                            break
                alpha_list = filtered_alpha_list
                self.logger.info(f"✅ 过滤后剩余 {len(alpha_list)} 个新Alpha")

            # 创建仿真任务
            tasks = self.create_simulation_tasks(alpha_list)
            if not tasks:
                return {'status': 'failed', 'error': '无法创建任务'}

            # 设置统计信息
            self.stats.total_tasks = len(tasks)
            self.stats.pending_tasks = len(tasks)
            self.stats.total_alphas = sum(len(task.alpha_list) for task in tasks)

            # 将任务加入队列
            for task in tasks:
                await self.task_queue.put(task)

            self.logger.info(f"🚀 开始启动 {self.max_slots} 个槽位...")

            # 启动所有槽位
            workers = []
            for slot_id in range(self.max_slots):
                worker = asyncio.create_task(self.slot_worker(slot_id))
                workers.append(worker)

            await asyncio.sleep(5)  # 等待部分槽位启动
            self.display_realtime_stats()  # 显示初始统计

            # 等待所有任务完成
            await self.task_queue.join()

            # 发送结束信号
            for _ in range(self.max_slots):
                await self.task_queue.put(None)

            # 等待所有槽位结束
            await asyncio.gather(*workers, return_exceptions=True)

            # 保存最终进度
            self.save_progress()

            # 计算最终统计
            end_time = time.time()
            duration = end_time - start_time

            final_stats = {
                'status': 'completed',
                'start_time': datetime.fromtimestamp(start_time),
                'end_time': datetime.fromtimestamp(end_time),
                'duration_seconds': duration,
                'total_tasks': len(tasks),
                'completed_tasks': len(self.completed_tasks),
                'failed_tasks': len(self.failed_tasks),
                'total_alphas': self.stats.total_alphas,
                'successful_alphas': self.stats.successful_alphas,
                'success_rate': self.stats.success_rate,
                'alphas_per_minute': self.stats.alphas_per_minute,
                'successful_alphas_file': self.successful_alphas_file,
                'progress_file': self.progress_file
            }

            self.display_final_report(final_stats)
            return final_stats

        except Exception as e:
            self.logger.error(f"❌ 流水线运行异常: {e}")
            try:
                self.save_progress()
                total_progress = self.resume_from + self.completed_task_count
                self.logger.info(f"💾 异常退出前已保存进度: 总进度{total_progress}个任务")
            except:
                pass
            import traceback
            traceback.print_exc()
            return {'status': 'failed', 'error': str(e)}

    def display_final_report(self, stats: Dict):
        """显示最终报告"""
        self.logger.info("=" * 80)
        self.logger.info("🎯 优化仿真流水线最终报告")
        self.logger.info("=" * 80)
        self.logger.info(f"📊 总任务数: {stats['total_tasks']}")
        self.logger.info(f"✅ 完成任务: {stats['completed_tasks']}")
        self.logger.info(f"❌ 失败任务: {stats['failed_tasks']}")
        self.logger.info(f"🎯 总Alpha数: {stats['total_alphas']}")
        self.logger.info(f"🆔 成功Alpha: {stats['successful_alphas']}")
        self.logger.info(f"📈 成功率: {stats['success_rate']:.1f}%")
        self.logger.info(f"⚡ 平均效率: {stats['alphas_per_minute']:.1f} Alpha/分钟")
        duration_min = stats['duration_seconds'] / 60
        self.logger.info(f"⏱️ 总耗时: {duration_min:.2f} 分钟")

        total_progress = self.resume_from + self.completed_task_count
        if self.resume_from > 0:
            self.logger.info(f"🔄 断点信息: 起始位置{self.resume_from} + 本次完成{self.completed_task_count} = 总进度{total_progress}")
        else:
            self.logger.info(f"📊 总进度: {total_progress}个任务完成")

        self.logger.info(f"📝 成功Alpha记录: {stats['successful_alphas_file']}")
        self.logger.info(f"💾 进度文件: {stats['progress_file']}")
        self.logger.info("=" * 80)

    @staticmethod
    def find_latest_progress_file() -> Optional[str]:
        """查找最新的进度文件"""
        import glob
        progress_files = glob.glob("simulation_progress_*.json")
        if not progress_files:
            return None

        # 按文件的最后修改时间排序，返回最新的那个文件
        latest_file = max(progress_files, key=os.path.getmtime)
        return latest_file

    @staticmethod
    def load_progress_info(progress_file: str) -> Optional[Dict]:
        """加载进度信息"""
        try:
            with open(progress_file, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception:
            return None

    @staticmethod
    def detect_resume_point() -> Tuple[int, Optional[Dict]]:
        """自动检测断点续传位置"""
        progress_file = OptimizedSimulationPipeline.find_latest_progress_file()
        if not progress_file:
            return 0, None

        progress_info = OptimizedSimulationPipeline.load_progress_info(progress_file)
        if not progress_info:
            return 0, None

        completed_tasks = progress_info.get('completed_tasks', 0)
        max_progress_ever = progress_info.get('max_progress_ever', completed_tasks)
        suggested_resume = max(completed_tasks, max_progress_ever)

        total_tasks = progress_info.get('total_tasks', 0)

        progress_info['suggested_resume'] = suggested_resume
        progress_info['progress_percentage'] = (suggested_resume / total_tasks * 100) if total_tasks > 0 else 0
        progress_info['remaining_tasks'] = total_tasks - suggested_resume
        progress_info['current_vs_max'] = f"当前:{completed_tasks}, 历史最大:{max_progress_ever}, 建议:{suggested_resume}"

        return suggested_resume, progress_info