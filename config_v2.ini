[dates]
use_dynamic_dates = False
; 当use_dynamic_dates为false时使用以下固定日期
start_date = 12-01
end_date = 12-07

[setting]
dataset_id = model26
prefix = winsorize
region = ASI
universe = MINVOL1M
delay = 1
decay = 0
instrument_type = EQUITY
neutralization = SUBINDUSTRY
truncation = 0.08
pasteurization = ON
unit_handling = VERIFY
nan_handling = OFF
language = FASTEXPR
visualization = false
max_trade = ON
test_period = P0Y

[others]
events = open_events
batch_size = 500
alpha_num_filter = 2000
third_promote_num = 200
alpha_num_submit = 500
second_promote_sharp = 1.2
second_promote_fitness = 0.7
third_promote_sharp = 1.3
third_promote_fitness = 0.8
submit_sharp_th = 1.58
submit_fitness_th = 1
submit_margin_th = 1
is_simulate = True
is_second_promote = True
is_third_promote = True
fields_file = filtered_shuffled_expressions.txt
coverage = 0.8
is_prune = False
is_filter_pnl = False
is_preprocess_alpha = True

[rate_limiting]
# 速率限制配置
max_concurrent_tasks = 3
task_check_interval = 10
request_delay = 2.0
retry_delay = 60
max_retries = 3
safety_margin = 0.8

[database]
; SQLite数据库配置
db_file = data/worldquant.db
; 是否启用数据库功能
enabled = True
; 是否存储所有alpha（包括不合格的）
store_all_alphas = True
; 数据库连接超时时间（秒）
timeout = 10

[async_database]
# 是否启用异步数据库操作
enabled = true
# 线程池大小
thread_pool_size = 3
# 任务队列最大大小
queue_max_size = 1000
# 操作超时时间（秒）
operation_timeout = 30
# 关闭时等待完成的超时时间（秒）
shutdown_timeout = 60