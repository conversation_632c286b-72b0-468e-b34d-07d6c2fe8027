#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单转换Alpha表达式格式，不过滤任何内容
"""

import re
import os

def simple_convert_expression(expr):
    """
    简单转换单个表达式，只处理格式问题
    """
    expr = expr.strip()
    if not expr or expr.startswith('#'):
        return None
    
    # 处理residual变量赋值格式
    if 'residual =' in expr and ';' in expr:
        parts = expr.split(';')
        if len(parts) == 2:
            residual_def = parts[0].split('=', 1)[1].strip()
            final_expr = parts[1].strip()
            
            # 替换final_expr中的residual引用
            if 'residual/' in final_expr:
                final_expr = final_expr.replace('residual/', f'({residual_def})/')
                final_expr = final_expr.replace('residual,', f'({residual_def}),')
                final_expr = final_expr.replace('residual)', f'({residual_def}))')
            
            expr = final_expr
    
    # 移除函数名后的空格
    expr = re.sub(r'(\w+)\s+\(', r'\1(', expr)
    
    return expr

def main():
    """主函数"""
    input_file = "decoded_expressions (21).txt"
    output_file = "direct_expressions.txt"
    
    print(f"🔄 简单转换文件: {input_file}")
    
    if not os.path.exists(input_file):
        print(f"❌ 文件不存在: {input_file}")
        return
    
    converted_expressions = []
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        for line in lines[:10]:  # 只取前10个进行测试
            converted = simple_convert_expression(line)
            if converted:
                converted_expressions.append(converted)
        
        # 写入转换后的文件
        with open(output_file, 'w', encoding='utf-8') as f:
            for expr in converted_expressions:
                f.write(expr + '\n')
        
        print(f"✅ 转换完成:")
        print(f"   处理表达式: {len(converted_expressions)}")
        print(f"   输出文件: {output_file}")
        
        # 显示转换后的表达式
        print(f"\n📋 转换后的表达式:")
        for i, expr in enumerate(converted_expressions):
            print(f"   {i+1}. {expr[:100]}...")
        
    except Exception as e:
        print(f"❌ 转换失败: {e}")

if __name__ == '__main__':
    main()
